// GENERATED BY SERVER CODE

/* API USE CASE
   For games with no levels, use Endgame (inject add score code, etc)
   For games with levels use Level events (Begin,End,Win,Lose,Draw)
*/

var _SETTINGS = {
    'API':{
        'Enabled':false,
        'Log':{
            'Events':{
                'InitializeGame':false,
                'EndGame':false,
                'Level':{
                    'Begin':false,
                    'End':false,
                    'Win':false,
                    'Lose':false,
                    'Draw':false,
                },
            },
        },
    },
    'Ad':{
        'Mobile':{
            'Preroll':{
                'Enabled':false,
                'Duration':5,
                'Width':300,
                'Height':250,
                'Rotation':{
                    'Enabled':false,
                    'Weight':{
                        'MobileAdInGamePreroll':40,
                        'MobileAdInGamePreroll2':40,
                        'MobileAdInGamePreroll3':20,
                    },
                },
            },
            'Header':{
                'Enabled':false,
                'Duration':5,
                'Width':320,
                'Height':50,
                'Rotation':{
                    'Enabled':false,
                    'Weight':{
                        'MobileAdInGameHeader':40,
                        'MobileAdInGameHeader2':40,
                        'MobileAdInGameHeader3':20,
                    },
                },
            },
            'Footer':{
                'Enabled':false,
                'Duration':5,
                'Width':320,
                'Height':50,
                'Rotation':{
                    'Enabled':false,
                    'Weight':{
                        'MobileAdInGameFooter':40,
                        'MobileAdInGameFooter2':40,
                        'MobileAdInGameFooter3':20,
                    },
                },
            },
            'End':{
                'Enabled':false,
                'Duration':1,
                'Width':300,
                'Height':250,
                'Rotation':{
                    'Enabled':false,
                    'Weight':{
                        'MobileAdInGameEnd':40,
                        'MobileAdInGameEnd2':40,
                        'MobileAdInGameEnd3':20,
                    },
                },
            },
        },
    },

    'Language':{
        'Default':'en',
    },

    'MoreGames':{
        'Enabled':true,
        'Link':'http://www.marketjs.com/game/links/mobile',
        'NewWindow': true,    // open link in new window, although this behavior can be override by browsers preference
    },

    'StoryCollection':{
        'Link':'http://www.marketjs.com',
        'NewWindow': true,    // open link in new window, although this behavior can be override by browsers preference
    },
    
    'TapToStartAudioUnlock': {
        'Enabled':false
    },

    'Versioning': {
        'Version': '2.0.0',
        'Build': '360',

        'DisplayLog': false,
        'DrawVersion': false,

        'FontSize': '16px',
        'FontFamily': 'Arial',
        'FillStyle': '#FFFFFF'
    }
};

if (typeof _SETTINGS.Versioning !== 'undefined' && _SETTINGS.Versioning !== null) {
    if (_SETTINGS.Versioning.DisplayLog === false) {
        console.log('Engine Release: v' + _SETTINGS.Versioning.Version + '+build.' + _SETTINGS.Versioning.Build);
    }
}

var _emoEye = {
 "EMO_DEFAULT": {
  "pngFile": "emo_default",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1 ]"
 },
 "EMO_NEUTRAL": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_HAPPY": {
  "pngFile": "emo_happy",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_SHOCK": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_BLUSH": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_ANGRY": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": true,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_SAD": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": true,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_SNEAKY": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_EYE_ROLL": {
  "pngFile": "emo_eye_roll",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.1,
  "sequence": "[ 0, 0, 2, 2, 3, 3, 1, 1, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3 ]",
  "sequence2": "[ 3, 3, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_SCARY": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_DISGUST": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": true,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_SCARED": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": true,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_HORRIFIED": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": true,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_GRUDGE": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": true,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_BITTERSWEET": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_LAUGH": {
  "pngFile": "emo_happy",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_EYE_CLOSED": {
  "pngFile": "emo_eye_closed",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_DISAPPOINTED": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": true,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_EXORCISED": {
  "pngFile": "emo_exorcised",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_SKEPTICAL": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": true,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_SURPRISED": {
  "pngFile": "emo_neutral",
  "stop": false,
  "mouthFrown": true,
  "frameTime": 0.2,
  "sequence": "[ 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0 ]"
 },
 "EMO_WINK": {
  "pngFile": "emo_wink",
  "stop": false,
  "mouthFrown": false,
  "frameTime": 0.2,
  "sequence": "[ 2, 2, 2, 2, 2, 2, 2, 2 ]",
  "sequence2": "[ 3, 3, 3, 3, 3, 3, 3, 3 ]"
 }
};
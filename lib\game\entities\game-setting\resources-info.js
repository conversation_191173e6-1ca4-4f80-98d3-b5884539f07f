var _RESOURCESINFO = {
	image:{
		bgMenu:'wardrobe.png',
		bg:_BASEPATH.background+'wardrobe.png',
		title:_BASEPATH.mediaGame+'graphics/sprites/title.png',
		brand:_BASEPATH.media + 'graphics/sprites/brand.png',
		lock:_BASEPATH.ui + _DATAGAME.uiTheme + '/lock.png',
		lockChapter:_BASEPATH.ui + _DATAGAME.uiTheme + '/lock-chapter.png',
		buttonExit:_BASEPATH.ui + _DATAGAME.uiTheme + '/button-exit.png',
		buttonCloseBig:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-close-big.png',
		btnHome:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-home.png',
		btnNext:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-next.png',
		btnBlank4Hover:_BASEPATH.ui + _DATAGAME.uiTheme + "/btn-blank4-hover.png",
		btnBlank4:_BASEPATH.ui + _DATAGAME.uiTheme + "/btn-blank4.png",
		btnBlank3:_BASEPATH.ui + _DATAGAME.uiTheme + "/btn-blank3.png",
		btnBlank2:_BASEPATH.ui + _DATAGAME.uiTheme + "/btn-blank2.png",
		btnBlank:_BASEPATH.ui + _DATAGAME.uiTheme + "/btn-blank.png",
		btnBlankHover:_BASEPATH.ui + _DATAGAME.uiTheme + "/btn-blank-hover.png",
		btnBlank3Grey:_BASEPATH.ui + _DATAGAME.uiTheme + "/btn-blank3-grey.png",
		btnFollow:_BASEPATH.ui + _DATAGAME.uiTheme + "/btn-follow.png",
		bgFollow:_BASEPATH.ui + _DATAGAME.uiTheme + '/bg-follow.png',
		btnPrev:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-prev.png',
		btnShop:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-shop.png',
		btnMoreGames:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-moregames.png',
		btnSoundOn:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-sound-on.png',
		btnSoundOff:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-sound-off.png',
		btnBGMOn:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-bgm-on.png',
		btnBGMOff:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-bgm-off.png',
		chapterPanel:_BASEPATH.ui + _DATAGAME.uiTheme + '/chapter-panel.png',
		bgChapter:_BASEPATH.ui + _DATAGAME.uiTheme + '/bg-chapter.png',
		bgChapter1:_BASEPATH.ui + _DATAGAME.uiTheme + '/bg-chapter1.png',
		bgChapter2:_BASEPATH.ui + _DATAGAME.uiTheme + '/bg-chapter2.png',
		bgChapter3:_BASEPATH.ui + _DATAGAME.uiTheme + '/bg-chapter3.png',
		bgInput:_BASEPATH.ui + _DATAGAME.uiTheme + '/bg-input.png',
		bgShop:_BASEPATH.ui + _DATAGAME.uiTheme + '/bg-shop.png',
		btnExpand:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-expand.png',
		btnShrink:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-shrink.png',
		btnSetting:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-setting.png',

		btnAuto:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-auto.png',
		btnAuto2:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-auto2.png',
		btnHide:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-hide.png',
		btnLoad:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-load.png',
		btnLog:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-log.png',
		btnSave:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-save.png',
		btnShow:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-show.png',
		btnSkip:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-skip.png',
		btnSkip2:_BASEPATH.ui + _DATAGAME.uiTheme + '/btn-skip2.png',

		globe:_BASEPATH.ui + _DATAGAME.uiTheme + '/globe.png',
		vc1:_BASEPATH.ui + _DATAGAME.uiTheme + '/vc1.png',
		vc2:_BASEPATH.ui + _DATAGAME.uiTheme + '/vc2.png',
		vc3:_BASEPATH.ui + _DATAGAME.uiTheme + '/vc3.png',
		vc4:_BASEPATH.ui + _DATAGAME.uiTheme + '/vc1.png',
		vc5:_BASEPATH.ui + _DATAGAME.uiTheme + '/vc2.png',
		vc6:_BASEPATH.ui + _DATAGAME.uiTheme + '/vc3.png',
		vc7:_BASEPATH.ui + _DATAGAME.uiTheme + '/vc1.png',
		vc8:_BASEPATH.ui + _DATAGAME.uiTheme + '/vc2.png',
		crown:_BASEPATH.ui + _DATAGAME.uiTheme + '/icon-crown.png',
		check:_BASEPATH.ui + _DATAGAME.uiTheme + '/icon-check.png',
		checkChapter:_BASEPATH.ui + _DATAGAME.uiTheme + '/icon-check-chapter.png',
		phone:_BASEPATH.image+'sprites/message/phone.png',
		shift:_BASEPATH.image+'responsive-keyboard/shift.png',
		enter:_BASEPATH.image+'responsive-keyboard/enter.png',
		backspace:_BASEPATH.image+'responsive-keyboard/backspace.png',
		bgLetter:_BASEPATH.image+'sprites/letter1.png',
		bgEmail:_BASEPATH.image+'sprites/email1.png',
	},
	spriter:{
		boy:_BASEPATH.spriter+"boy/boy.scml",
		girl:_BASEPATH.spriter+"girl/girl.scml"
	},
	bgm:'bgm-insta'
};
mergeObject(_RESOURCESINFO,_GAMESETTING._RESOURCESINFO);
mergeObject=null;
// // ig.resourcesInfo=new ResourcesInfo();
// console.log("aaa");